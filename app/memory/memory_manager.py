"""
Memory manager for handling short-term and long-term memory using LangMem.
"""
import asyncio
import datetime
from typing import Dict, List, Any, Optional

from langmem import utils
from qdrant_client import QdrantClient
from qdrant_client.http import models

from app.logging_custom_directory.logger_custom import logger
from app.memory.langmem_config import (
    QDRANT_URL,
    QDRANT_COLLECTION_NAME,
)

# Initialize the memory store
def get_memory_store():
    """Get the memory store for LangM<PERSON>."""
    try:
        # Use Qdrant for vector storage
        client = QdrantClient(url=QDRANT_URL)
        logger.info(f"Initialized Qdrant memory store at {QDRANT_URL}")
        return client
    except Exception as e:
        logger.error(f"Error initializing Qdrant memory store: {e}")
        # Fallback to in-memory store
        logger.warning("Falling back to in-memory store")
        return None

# Helper function to get embeddings
def get_embeddings(text: str) -> List[float]:
    """
    Get embeddings for text using LangMem's utility function.

    Args:
        text: The text to embed

    Returns:
        List of embedding values
    """
    try:
        return utils.get_embeddings(text)
    except Exception as e:
        logger.error(f"Error getting embeddings: {e}")
        raise

async def store_user_message(
    user_id: str,
    chat_id: str,
    messages: List[Dict[str, Any]],
    run_async: bool = True
) -> bool:
    """
    Store a user message in memory.

    Args:
        user_id: The user ID
        chat_id: The chat ID
        messages: The message objects to store
        run_async: Whether to run the storage operation asynchronously

    Returns:
        Success status
    """
    async def _store():
        try:
            # Get the Qdrant client
            client = get_memory_store()
            if not client:
                logger.error("Failed to get Qdrant client")
                return False

            # Format messages for storage
            formatted_messages = []
            for msg in messages:
                if isinstance(msg, dict) and "role" in msg and "content" in msg:
                    formatted_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif isinstance(msg, list) and len(msg) >= 2:
                    formatted_messages.append({"role": "user", "content": msg[0]})
                    formatted_messages.append({"role": "assistant", "content": msg[1]})

            # Extract the last user message for embedding
            last_user_message = None
            for msg in reversed(formatted_messages):
                if msg["role"] == "user":
                    last_user_message = msg["content"]
                    break

            if not last_user_message:
                logger.warning("No user message found to store")
                return False

            # Get embeddings for the message
            embeddings = get_embeddings(last_user_message)

            # Create metadata
            metadata = {
                "user_id": user_id,
                "chat_id": chat_id,
                "timestamp": datetime.datetime.now().isoformat(),
                "memory_type": "conversation"
            }

            # Create payload
            payload = {
                "content": last_user_message,
                "metadata": metadata,
                "messages": formatted_messages
            }

            # Store in Qdrant
            client.upsert(
                collection_name=QDRANT_COLLECTION_NAME,
                points=[
                    models.PointStruct(
                        id=f"{user_id}_{chat_id}_{datetime.datetime.now().timestamp()}",
                        vector=embeddings,
                        payload=payload
                    )
                ]
            )

            logger.info(f"Stored message in memory for user {user_id}, chat {chat_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing user message: {e}")
            return False

    if run_async:
        # Run asynchronously and return immediately
        asyncio.create_task(_store())
        return True
    else:
        # Run synchronously and wait for result
        return await _store()

async def search_memories(
    user_id: str,
    query: str,
    limit: int = 5,
    include_conversation_history: bool = True,
    chat_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Search memories for a user.

    Args:
        user_id: The user ID
        query: The search query
        limit: Maximum number of results to return
        include_conversation_history: Whether to include conversation history
        chat_id: The chat ID (required if include_conversation_history is True)

    Returns:
        Dictionary with search results
    """
    try:
        # Get the memory store (Qdrant client)
        client = get_memory_store()
        if not client:
            logger.error("Failed to get Qdrant client")
            return {"results": []}

        # Get embeddings for the query
        embeddings = get_embeddings(query)

        # Search for memories in Qdrant
        search_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="metadata.user_id",
                    match=models.MatchValue(value=user_id)
                )
            ]
        )

        # Add chat_id filter if provided
        if chat_id and include_conversation_history:
            search_filter.must.append(
                models.FieldCondition(
                    key="metadata.chat_id",
                    match=models.MatchValue(value=chat_id)
                )
            )

        # Search Qdrant
        search_results = client.search(
            collection_name=QDRANT_COLLECTION_NAME,
            query_vector=embeddings,
            limit=limit,
            filter=search_filter
        )

        # Format results
        formatted_results = []
        for result in search_results:
            payload = result.payload or {}
            memory_content = payload.get("content", "")
            metadata = payload.get("metadata", {})

            formatted_results.append({
                "memory_id": str(result.id),
                "memory": memory_content,
                "metadata": metadata,
                "score": result.score
            })

        logger.info(f"Found {len(formatted_results)} memories for user {user_id}")
        return {"results": formatted_results}
    except Exception as e:
        logger.error(f"Error searching memories: {e}")
        return {"results": []}
