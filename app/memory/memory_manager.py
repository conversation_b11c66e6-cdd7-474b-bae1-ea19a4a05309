"""
Memory manager for handling short-term and long-term memory using Qdrant with OpenAI embeddings.
"""
import asyncio
import datetime
import uuid
from typing import Dict, List, Any, Optional

# Use OpenAI embeddings directly instead of langmem.utils
from openai import OpenAI
from qdrant_client import QdrantClient
from qdrant_client.http import models

from app.logging_custom_directory.logger_custom import logger
from app.memory.langmem_config import (
    QDRANT_URL,
    QDRANT_COLLECTION_NAME,
    MEMORY_EMBEDDING_MODEL,
)

# Initialize OpenAI client for embeddings
openai_client = OpenAI()

# Fallback memory store for when Qdrant is unavailable
class InMemoryFallbackStore:
    """Simple in-memory fallback store for when Qdrant is unavailable."""

    def __init__(self):
        self.memories = []
        self.next_id = 1

    def store_memory(self, content: str, metadata: Dict[str, Any], embeddings: List[float]) -> str:
        """Store a memory in the fallback store."""
        memory_id = str(self.next_id)
        self.next_id += 1

        memory = {
            "id": memory_id,
            "content": content,
            "metadata": metadata,
            "embeddings": embeddings,
            "timestamp": datetime.datetime.now().isoformat()
        }
        self.memories.append(memory)
        return memory_id

    def search_memories(self, query_embeddings: List[float], user_id: str,
                       chat_id: Optional[str] = None, limit: int = 5) -> List[Dict[str, Any]]:
        """Search memories in the fallback store using simple text matching."""
        # Filter by user_id and optionally chat_id
        filtered_memories = [
            m for m in self.memories
            if m["metadata"].get("user_id") == user_id and
            (chat_id is None or m["metadata"].get("chat_id") == chat_id)
        ]

        # For simplicity, return most recent memories
        # In a real implementation, you'd compute cosine similarity
        filtered_memories.sort(key=lambda x: x["timestamp"], reverse=True)
        return filtered_memories[:limit]

# Global fallback store instance
fallback_store = InMemoryFallbackStore()

# Initialize the memory store
def get_memory_store():
    """Get the memory store - either Qdrant or fallback."""
    try:
        # Use Qdrant for vector storage
        client = QdrantClient(url=QDRANT_URL)
        logger.info(f"Initialized Qdrant memory store at {QDRANT_URL}")
        return client
    except Exception as e:
        logger.error(f"Error initializing Qdrant memory store: {e}")
        # Return None to indicate Qdrant is unavailable
        logger.warning("Qdrant unavailable, will use fallback store")
        return None

# Helper function to get embeddings
def get_embeddings(text: str) -> List[float]:
    """
    Get embeddings for text using OpenAI's embedding API.

    Args:
        text: The text to embed

    Returns:
        List of embedding values
    """
    try:
        response = openai_client.embeddings.create(
            model=MEMORY_EMBEDDING_MODEL,
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error getting embeddings: {e}")
        raise

async def store_user_message(
    user_id: str,
    chat_id: str,
    messages: List[Dict[str, Any]],
    run_async: bool = True
) -> bool:
    """
    Store a user message in memory.

    Args:
        user_id: The user ID
        chat_id: The chat ID
        messages: The message objects to store
        run_async: Whether to run the storage operation asynchronously

    Returns:
        Success status
    """
    async def _store():
        try:
            # Get the Qdrant client
            client = get_memory_store()

            # Format messages for storage
            formatted_messages = []
            for msg in messages:
                if isinstance(msg, dict) and "role" in msg and "content" in msg:
                    formatted_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif isinstance(msg, list) and len(msg) >= 2:
                    formatted_messages.append({"role": "user", "content": msg[0]})
                    formatted_messages.append({"role": "assistant", "content": msg[1]})

            # Extract the last user message for embedding
            last_user_message = None
            for msg in reversed(formatted_messages):
                if msg["role"] == "user":
                    last_user_message = msg["content"]
                    break

            if not last_user_message:
                logger.warning("No user message found to store")
                return False

            # Get embeddings for the message
            embeddings = get_embeddings(last_user_message)

            # Create metadata
            metadata = {
                "user_id": user_id,
                "chat_id": chat_id,
                "timestamp": datetime.datetime.now().isoformat(),
                "memory_type": "conversation"
            }

            if client:
                # Store in Qdrant
                try:
                    # Create payload
                    payload = {
                        "content": last_user_message,
                        "metadata": metadata,
                        "messages": formatted_messages
                    }

                    client.upsert(
                        collection_name=QDRANT_COLLECTION_NAME,
                        points=[
                            models.PointStruct(
                                id=str(uuid.uuid4()),  # Use UUID for point ID
                                vector=embeddings,
                                payload=payload
                            )
                        ]
                    )

                    logger.info(f"Stored message in Qdrant for user {user_id}, chat {chat_id}")
                    return True
                except Exception as e:
                    logger.error(f"Error storing in Qdrant: {e}, falling back to in-memory store")
                    # Fall through to fallback storage

            # Use fallback storage
            memory_id = fallback_store.store_memory(
                content=last_user_message,
                metadata=metadata,
                embeddings=embeddings
            )
            logger.info(f"Stored message in fallback store for user {user_id}, chat {chat_id}, memory_id: {memory_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing user message: {e}")
            return False

    if run_async:
        # Run asynchronously and return immediately
        asyncio.create_task(_store())
        return True
    else:
        # Run synchronously and wait for result
        return await _store()

async def search_memories(
    user_id: str,
    query: str,
    limit: int = 5,
    include_conversation_history: bool = True,
    chat_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Search memories for a user.

    Args:
        user_id: The user ID
        query: The search query
        limit: Maximum number of results to return
        include_conversation_history: Whether to include conversation history
        chat_id: The chat ID (required if include_conversation_history is True)

    Returns:
        Dictionary with search results
    """
    try:
        # Get the memory store (Qdrant client)
        client = get_memory_store()

        # Get embeddings for the query
        embeddings = get_embeddings(query)

        formatted_results = []

        if client:
            # Search in Qdrant
            try:
                # Search for memories in Qdrant
                search_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="metadata.user_id",
                            match=models.MatchValue(value=user_id)
                        )
                    ]
                )

                # Add chat_id filter if provided
                if chat_id and include_conversation_history:
                    search_filter.must.append(
                        models.FieldCondition(
                            key="metadata.chat_id",
                            match=models.MatchValue(value=chat_id)
                        )
                    )

                # Search Qdrant
                search_results = client.search(
                    collection_name=QDRANT_COLLECTION_NAME,
                    query_vector=embeddings,
                    limit=limit,
                    query_filter=search_filter
                )

                # Format results
                for result in search_results:
                    payload = result.payload or {}
                    memory_content = payload.get("content", "")
                    metadata = payload.get("metadata", {})

                    formatted_results.append({
                        "memory_id": str(result.id),
                        "memory": memory_content,
                        "metadata": metadata,
                        "score": result.score
                    })

                logger.info(f"Found {len(formatted_results)} memories in Qdrant for user {user_id}")

            except Exception as e:
                logger.error(f"Error searching Qdrant: {e}, falling back to in-memory store")
                # Fall through to fallback search

        # If Qdrant failed or is unavailable, search fallback store
        if not formatted_results:
            fallback_results = fallback_store.search_memories(
                query_embeddings=embeddings,
                user_id=user_id,
                chat_id=chat_id if include_conversation_history else None,
                limit=limit
            )

            for result in fallback_results:
                formatted_results.append({
                    "memory_id": result["id"],
                    "memory": result["content"],
                    "metadata": result["metadata"],
                    "score": 0.8  # Default score for fallback results (need to adjust this but for now Qdrant is working)
                })

            logger.info(f"Found {len(formatted_results)} memories in fallback store for user {user_id}")

        return {"results": formatted_results}
    except Exception as e:
        logger.error(f"Error searching memories: {e}")
        return {"results": []}
